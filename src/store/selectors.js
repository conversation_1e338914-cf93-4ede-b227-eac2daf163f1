// 选择器函数 - 用于从 store 中获取特定数据

// 用户相关选择器
export const getUsers = (state) => state.users;
export const getUserMap = (state) => state.userMap;
export const getCurrentUser = (state) => state.currentUser;
export const getUserById = (state, userId) => state.userMap[userId];
export const getUsersByDepartment = (state, departmentId) =>
  state.users.filter((user) => user.departmentId === departmentId);
export const getUsersByRole = (state, roleId) =>
  state.users.filter((user) =>
    user.roles && user.roles.some((role) => role.id === roleId));

// 部门相关选择器
export const getDepartments = (state) => state.departments;
export const getDepartmentMap = (state) => state.departmentMap;
export const getDepartmentById = (state, departmentId) => state.departmentMap[departmentId];
export const getDepartmentName = (state, departmentId) => {
  const department = state.departmentMap[departmentId];
  return department ? department.name : '未知部门';
};
export const getDepartmentUsers = (state, departmentId) =>
  state.departmentUsers[departmentId] || [];

// 角色相关选择器
export const getRoles = (state) => state.roles;
export const getRoleMap = (state) => state.roleMap;
export const getRoleById = (state, roleId) => state.roleMap[roleId];
export const getRoleName = (state, roleId) => {
  const role = state.roleMap[roleId];
  return role ? role.name : '未知角色';
};
export const getRolesByIds = (state, roleIds) =>
  roleIds.map((id) => state.roleMap[id]).filter(Boolean);

// 权限相关选择器
export const getPermissions = (state) => state.permissions;
export const getPermissionMap = (state) => state.permissionMap;
export const getPermissionCategories = (state) => state.permissionCategories;
export const getPermissionById = (state, permissionId) => state.permissionMap[permissionId];
export const getPermissionsByCategory = (state, category) =>
  state.permissions.filter((permission) => permission.category === category);
export const getPermissionsByIds = (state, permissionIds) =>
  permissionIds.map((id) => state.permissionMap[id]).filter(Boolean);

// 品牌相关选择器
export const getBrands = (state) => state.brands;
export const getBrandMap = (state) => state.brandMap;
export const getBrandById = (state, brandId) => state.brandMap[brandId];
export const getBrandName = (state, brandId) => {
  const brand = state.brandMap[brandId];
  return brand ? brand.name : '未知品牌';
};

// 供应商相关选择器
export const getSuppliers = (state) => state.suppliers;
export const getSupplierMap = (state) => state.supplierMap;
export const getSupplierById = (state, supplierId) => state.supplierMap[supplierId];
export const getSupplierName = (state, supplierId) => {
  const supplier = state.supplierMap[supplierId];
  return supplier ? supplier.name : '未知供应商';
};

// 项目相关选择器
export const getProjects = (state) => state.projects;
export const getProjectMap = (state) => state.projectMap;
export const getProjectById = (state, projectId) => state.projectMap[projectId];
export const getProjectName = (state, projectId) => {
  const project = state.projectMap[projectId];
  return project ? project.name : '未知项目';
};

// 加载状态选择器
export const getLoading = (state) => state.loading;
export const isLoading = (state, key) => state.loading[key] || false;

// 错误状态选择器
export const getErrors = (state) => state.errors;
export const getError = (state, key) => state.errors[key];

// 复合选择器 - 组合多个数据源
export const getUsersWithDepartmentNames = (state) => {
  return state.users.map((user) => ({
    ...user,
    departmentName: getDepartmentName(state, user.departmentId),
  }));
};

export const getUsersWithRoleNames = (state) => {
  return state.users.map((user) => ({
    ...user,
    roleNames: user.roles ? user.roles.map((role) => role.name) : [],
  }));
};

export const getRolesWithPermissionNames = (state) => {
  return state.roles.map((role) => ({
    ...role,
    permissionNames: role.permissions ? role.permissions.map((permission) => permission.name) : [],
  }));
};

// 统计选择器
export const getUserStats = (state) => {
  const { users } = state;
  return {
    total: users.length,
    active: users.filter((user) => user.status === 'active').length,
    inactive: users.filter((user) => user.status === 'inactive').length,
    pending: users.filter((user) => user.status === 'pending').length,
  };
};

export const getRoleStats = (state) => {
  const { roles } = state;
  return {
    total: roles.length,
    active: roles.filter((role) => role.status === 'active').length,
    inactive: roles.filter((role) => role.status === 'inactive').length,
  };
};

export const getDepartmentStats = (state) => {
  const { departments } = state;
  const { users } = state;

  return departments.map((department) => ({
    ...department,
    userCount: users.filter((user) => user.departmentId === department.id).length,
  }));
};

// 搜索选择器
export const searchUsers = (state, keyword) => {
  if (!keyword) return state.users;

  const lowerKeyword = keyword.toLowerCase();
  return state.users.filter((user) =>
    user.name.toLowerCase().includes(lowerKeyword) ||
    user.mobile.includes(keyword) ||
    (user.email && user.email.toLowerCase().includes(lowerKeyword)));
};

export const searchRoles = (state, keyword) => {
  if (!keyword) return state.roles;

  const lowerKeyword = keyword.toLowerCase();
  return state.roles.filter((role) =>
    role.name.toLowerCase().includes(lowerKeyword) ||
    role.code.toLowerCase().includes(lowerKeyword) ||
    (role.description && role.description.toLowerCase().includes(lowerKeyword)));
};

export const searchPermissions = (state, keyword) => {
  if (!keyword) return state.permissions;

  const lowerKeyword = keyword.toLowerCase();
  return state.permissions.filter((permission) =>
    permission.name.toLowerCase().includes(lowerKeyword) ||
    permission.code.toLowerCase().includes(lowerKeyword) ||
    (permission.description && permission.description.toLowerCase().includes(lowerKeyword)));
};
