import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Input,
  Select,
  Row,
  Col,
  message,
  Tag,
  Avatar,
  Popconfirm,
  // Badge,
} from 'antd';
import { userApi, roleApi } from '../../services/api';
import { useDepartments } from '../../store/hooks';
import UserFormModal from './UserFormModal';
import RoleAssignModal from './RoleAssignModal';
import SyncUsersModal from './SyncUsersModal';

const { Search } = Input;
const { Option } = Select;

const UserManagement = () => {
  // 使用 store hooks
  const { getDepartmentName, departments } = useDepartments();
  // 本地状态
  const [loading, setLoading] = useState(false);
  const [users, setUsers] = useState([]);
  const [roles, setRoles] = useState([]);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 20,
    total: 0,
  });
  const [filters, setFilters] = useState({
    status: undefined,
    departmentId: undefined,
    roleId: undefined,
    keyword: '',
  });
  const [userFormVisible, setUserFormVisible] = useState(false);
  const [roleAssignVisible, setRoleAssignVisible] = useState(false);
  const [syncUsersVisible, setSyncUsersVisible] = useState(false);
  const [selectedUser, setSelectedUser] = useState(null);
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);

  // 加载用户列表
  const loadUsers = async (params = {}) => {
    console.log('[ loadUsers ] >', 'loadUsers');
    setLoading(true);

    try {
      const requestParams = {
        page: pagination.current,
        pageSize: pagination.pageSize,
        ...filters,
        ...params,
      };

      const response = await userApi.getUsers(requestParams);
      console.log('[ response ] >', response);

      if (response.success) {
        setUsers(response.data.users || []);
        setPagination((prev) => ({
          ...prev,
          total: response.data.total || 0,
        }));
      } else {
        message.error(response.message || '获取用户列表失败');
      }
    } catch (error) {
      console.error('Load users failed:', error);
      message.error('获取用户列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 组件挂载时加载数据
  useEffect(() => {
    loadUsers();
    loadRoles();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // 监听 filters 和 pagination 变化，重新加载数据
  useEffect(() => {
    loadUsers();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [filters, pagination.current, pagination.pageSize]);

  // 加载角色列表
  const loadRoles = async () => {
    try {
      const response = await roleApi.getRoles({ pageSize: 1000 });
      if (response.success) {
        setRoles(response.data.list || []);
      }
    } catch (error) {
      console.error('Load roles failed:', error);
    }
  };

  // 处理搜索
  const handleSearch = (keyword) => {
    setFilters((prev) => ({ ...prev, keyword }));
    setPagination((prev) => ({ ...prev, current: 1 }));
    // 这里需要在 useEffect 中监听 filters 变化来重新加载数据
  };

  // 处理筛选
  const handleFilterChange = (key, value) => {
    setFilters((prev) => ({ ...prev, [key]: value }));
    setPagination((prev) => ({ ...prev, current: 1 }));
    // 这里需要在 useEffect 中监听 filters 变化来重新加载数据
  };

  // 处理分页
  const handleTableChange = (newPagination) => {
    setPagination(newPagination);
    // 这里需要在 useEffect 中监听 pagination 变化来重新加载数据
  };

  // 打开用户表单
  const handleOpenUserForm = (user = null) => {
    setUserFormVisible(true);
    setSelectedUser(user);
  };

  // 关闭用户表单
  const handleCloseUserForm = () => {
    setUserFormVisible(false);
    setSelectedUser(null);
  };

  // 用户表单提交成功
  const handleUserFormSuccess = () => {
    handleCloseUserForm();
    loadUsers();
    message.success('操作成功');
  };

  // 打开角色分配
  const handleOpenRoleAssign = (user) => {
    setRoleAssignVisible(true);
    setSelectedUser(user);
  };

  // 关闭角色分配
  const handleCloseRoleAssign = () => {
    setRoleAssignVisible(false);
    setSelectedUser(null);
  };

  // 角色分配成功
  const handleRoleAssignSuccess = () => {
    handleCloseRoleAssign();
    loadUsers();
    message.success('角色分配成功');
  };

  // 更新用户状态
  const handleUpdateUserStatus = async (userId, status) => {
    try {
      const response = await userApi.updateUserStatus(userId, status);
      if (response.success) {
        message.success('用户状态更新成功');
        loadUsers();
      } else {
        message.error(response.message || '用户状态更新失败');
      }
    } catch (error) {
      console.error('Update user status failed:', error);
      message.error('用户状态更新失败');
    }
  };

  // 打开同步用户
  const handleOpenSyncUsers = async () => {
    setSyncUsersVisible(true);
    // try {
    //   await userApi.syncDingTalkUsers({ force: true });
    //   await loadUsers();
    //   message.success('同步成功');
    // } catch (error) {
    //   console.error('Sync users failed:', error);
    //   message.error('用户同步失败');
    // } finally {
    //   setSyncUsersVisible(false);
    // }
  };

  // 关闭同步用户
  const handleCloseSyncUsers = () => {
    setSyncUsersVisible(false);
  };

  // 同步用户成功
  const handleSyncUsersSuccess = () => {
    handleCloseSyncUsers();
    loadUsers();
    message.success('用户同步成功');
  };


  // 渲染状态标签
  const renderStatusTag = (status) => {
    // stateCode: "86"
    const statusMap = {
      // stateCode: "86"
      86: { color: 'green', text: '正常' },
      inactive: { color: 'red', text: '禁用' },
      pending: { color: 'orange', text: '待激活' },
    };

    const config = statusMap[status] || { color: 'default', text: '未知' };
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  // 渲染角色标签
  const renderRoleTags = (userRoles) => {
    if (!userRoles || userRoles.length === 0) {
      return <Tag color="">无角色</Tag>;
    }

    return userRoles.slice(0, 2).map((role) => (
      <Tag key={role.id} color="blue">
        {role.displayName}
      </Tag>
    ));
  };

  // 定义表格列

  const columns = [
    {
      title: '用户信息',
      key: 'userInfo',
      width: 200,
      render: (_, record) => (
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <Avatar
            size="small"
            src={record.avatar}
            icon="user"
            style={{ marginRight: 8 }}
          />
          <div>
            <div style={{ fontWeight: 'bold' }}>{record.name}</div>
            <div style={{ fontSize: '12px', color: '#666' }}>
              {record.mobile}
            </div>
          </div>
        </div>
      ),
    },
    {
      title: '钉钉ID',
      dataIndex: 'userid',
      width: 120,
      render: (userId) => userId || '-',
    },
    {
      title: '部门',
      dataIndex: 'deptIdList',
      width: 120,
      render: (deptIdList) => {
        if (!deptIdList || deptIdList.length === 0) {
          return '无部门';
        }
        // 多个部门处理
        const departmentNames = deptIdList
          .filter((deptId) => deptId !== 1)
          .map((deptId) => getDepartmentName(deptId));
        if (departmentNames.length === 1) {
          return departmentNames[0];
        }
        return departmentNames.join(', ');
      },
    },
    {
      title: '角色',
      dataIndex: 'roles',
      width: 150,
      render: (r) => renderRoleTags(r),
    },
    {
      // stateCode: "86"
      title: '状态',
      dataIndex: 'stateCode',
      width: 80,
      render: (status) => renderStatusTag(status),
    },
    {
      title: '最后登录',
      dataIndex: 'lastLoginAt',
      width: 120,
      render: (time) => (time ? new Date(time).toLocaleDateString() : '-'),
    },
    {
      title: '操作',
      key: 'actions',
      width: 200,
      render: (_, record) => (
        <div>
          <Button
            type="link"
            size="small"
            onClick={() => handleOpenUserForm(record)}
          >
            编辑
          </Button>
          <Button
            type="link"
            size="small"
            onClick={() => handleOpenRoleAssign(record)}
          >
            分配角色
          </Button>
          <Popconfirm
            title={`确定要${record.status === 'active' ? '禁用' : '启用'}该用户吗？`}
            onConfirm={() =>
              handleUpdateUserStatus(
                record.id,
                record.status === 'active' ? 'inactive' : 'active',
              )
              }
          >
            <Button
              type="link"
              size="small"
              style={{
                color: record.status === 'active' ? '#ff4d4f' : '#52c41a',
              }}
            >
              {record.status === 'active' ? '禁用' : '启用'}
            </Button>
          </Popconfirm>
        </div>
      ),
    },
  ];

  return (
    <div style={{ padding: '24px' }}>
      <Card
        title="用户管理"
        extra={
          <div>
            <Button
              type="primary"
              onClick={handleOpenSyncUsers}
              style={{ marginRight: 8 }}
            >
              同步钉钉用户
            </Button>
            <Button type="primary" onClick={() => handleOpenUserForm()}>
              新增用户
            </Button>
          </div>
          }
      >
        {/* 筛选区域 */}
        <Row gutter={16} style={{ marginBottom: 16 }}>
          <Col span={6}>
            <Search
              placeholder="搜索用户名、手机号"
              onSearch={handleSearch}
              allowClear
            />
          </Col>
          <Col span={4}>
            <Select
              placeholder="选择状态"
              value={filters.status}
              onChange={(value) => handleFilterChange('status', value)}
              allowClear
              style={{ width: '100%' }}
            >
              <Option value="active">正常</Option>
              <Option value="inactive">禁用</Option>
              <Option value="pending">待激活</Option>
            </Select>
          </Col>
          <Col span={4}>
            <Select
              placeholder="选择部门"
              value={filters.departmentId}
              onChange={(value) => handleFilterChange('departmentId', value)}
              allowClear
              style={{ width: '100%' }}
            >
              {departments.map((dept) => (
                <Option key={dept.id} value={dept.id}>
                  {dept.name}
                </Option>
              ))}
            </Select>
          </Col>
          <Col span={4}>
            <Select
              placeholder="选择角色"
              value={filters.roleId}
              onChange={(value) => handleFilterChange('roleId', value)}
              allowClear
              style={{ width: '100%' }}
            >
              {roles.map((role) => (
                <Option key={role.id} value={role.id}>
                  {role.name}
                </Option>
              ))}
            </Select>
          </Col>
        </Row>

        {/* 用户表格 */}
        <Table
          columns={columns}
          dataSource={users}
          rowKey="id"
          loading={loading}
          pagination={{
            ...pagination,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`,
          }}
          onChange={handleTableChange}
          rowSelection={{
            selectedRowKeys,
            onChange: (keys) => setSelectedRowKeys(keys),
          }}
        />
      </Card>

      {/* 用户表单弹窗 */}
      {userFormVisible && (
      <UserFormModal
        visible={userFormVisible}
        user={selectedUser}
        roles={roles}
        onCancel={handleCloseUserForm}
        onSuccess={handleUserFormSuccess}
      />
      )}

      {/* 角色分配弹窗 */}
      {roleAssignVisible && (
      <RoleAssignModal
        visible={roleAssignVisible}
        user={selectedUser}
        roles={roles}
        onCancel={handleCloseRoleAssign}
        onSuccess={handleRoleAssignSuccess}
      />
      )}

      {/* 同步用户弹窗 */}
      {syncUsersVisible && (
      <SyncUsersModal
        visible={syncUsersVisible}
        onCancel={handleCloseSyncUsers}
        onSuccess={handleSyncUsersSuccess}
      />
      )}
    </div>
  );
};

export default UserManagement;
