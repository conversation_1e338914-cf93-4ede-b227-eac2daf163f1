import React, { Component } from 'react';
import { Card, Icon, Tabs, Button } from 'antd';
import BrandSummary from './BrandSummary';
import BrandExecution from './BrandExecution';
import { financialExportAPI } from '../../services/financialAPI';
import './ReportsPage.css';
import dd from 'dingtalk-jsapi';

const { TabPane } = Tabs;

class ReportsPage extends Component {
  constructor(props) {
    super(props);
    this.state = {
      // loading: false,
      activeTab: this.getInitialTab(),
      // overallStats: {
      //   totalRevenue: 0,
      //   totalCost: 0,
      //   totalProfit: 0,
      //   profitMargin: 0,
      // },
    };
  }

  componentDidMount() {
    this.loadOverallStats();
  }

  // 加载总体统计数据
  loadOverallStats = async () => {
    // this.setState({ loading: true });
    // this.setState({ loading: true });
    try {
      // 并行获取项目统计和收入统计
      // const [projectStatsResponse, revenueStatsResponse] = await Promise.all([
      //   projectAPI.getProjectStats(),
      //   revenueAPI.getRevenueStats(),
      // ]);

      // let totalRevenue = 0;
      // let totalCost = 0;
      // let totalProfit = 0;
      // let profitMargin = 0;

      // // 处理项目统计数据
      // if (projectStatsResponse.success) {
      //   const projectStats = projectStatsResponse.data;
      //   totalCost = projectStats.totalCost || 0;
      //   totalProfit = projectStats.totalProfit || 0;
      // }

      // // 处理收入统计数据
      // if (revenueStatsResponse.success) {
      //   const revenueStats = revenueStatsResponse.data;
      //   totalRevenue = revenueStats.totalActualRevenue || revenueStats.totalPlannedRevenue || 0;
      // }

      // // 计算利润率
      // if (totalRevenue > 0) {
      //   profitMargin = (totalProfit / totalRevenue) * 100;
      // }

      // this.setState({
      //   overallStats: {
      //     totalRevenue,
      //     totalCost,
      //     totalProfit,
      //     profitMargin,
      //   },
      // });
    } catch (error) {
      console.error('Load overall stats failed:', error);
      // 使用模拟数据作为后备
      // this.setState({
      //   overallStats: {
      //     totalRevenue: 1250000000,
      //     totalCost: 850000,
      //     totalProfit: 400000,
      //     profitMargin: 32,
      //   },
      // });
    } finally {
      // this.setState({ loading: false });
      // this.setState(() => ({
      //   loading: false,
      // }));
    }
  };
  getInitialTab = () => {
    const urlParams = new URLSearchParams(window.location.search);
    const tab = urlParams.get('tab');
    return tab || 'brand-summary';
  };

  handleTabChange = (key) => {
    this.setState({ activeTab: key });

    // 更新URL参数
    const url = new URL(window.location);
    if (key === 'financial') {
      url.searchParams.delete('tab');
    } else {
      url.searchParams.set('tab', key);
    }
    window.history.pushState({}, '', url);
  };
  exportReports = () => {
    // TODO: 实现导出报表的逻辑
    console.log('导出报表');
    // loading

    // api/financial/export?year=2025
    financialExportAPI.exportReport({ year: 2025 }).then((data) => {
      console.log(data);
      console.log(window.location.origin);
      console.log(`${window.location.origin}/${data.url}`);
      console.log(
        '[ encodeURIComponent(`$) ] >',
        encodeURIComponent(`${window.location.origin}/${data.url}`),
      );
      console.log('[ data.filename ] >', data.filename);
      dd.biz.util.openLink({
        url: `${window.location.origin}/export/${data.url}`,
        onSuccess: () => {
          console.log('打开链接成功');
        },
        onFail: (err) => {
          console.error('打开链接失败:', err);
        },
      });
      // 加载loading
      // dd.biz.util.downloadFile({
      //   url: `${window.location.origin }/${data.url}`,
      //   name: `${data.filename}`,
      //   onProgress: (info) => {
      //     console.log('下载进度:', info);
      //     dd.device.notification.showPreloader({
      //       text: `正在导出报表，请稍后... ${info.percent}%`,
      //     });
      //   },
      //   onSuccess: () => {
      //     console.log('下载成功');
      //     dd.device.notification.alert({
      //       message: '报表导出成功，请在钉钉文件中查看',
      //       title: '提示',
      //       buttonName: '确定',
      //     });
      //   },
      //   onFail: (err) => {
      //     console.error('下载失败:', err);
      //     dd.device.notification.alert({
      //       message: '报表导出失败，请重试',
      //       title: '提示',
      //       buttonName: '确定',
      //     });
      //   },
      // });
    });
    //
  };

  render() {
    const { activeTab } = this.state;

    return (
      <div className="modern-reports-page">
        {/* 页面头部 */}
        <div className="page-header">
          <div className="header-content">
            <div className="header-left">
              <h1 className="page-title">
                <Icon type="bar-chart" className="title-icon" />
                报表分析
              </h1>
              <p className="page-description">
                查看项目财务数据和分析报告
              </p>
            </div>
            <div className="header-actions">
              <Button
                type="primary"
                size="large"
                icon="download"
                className="action-button"
                onClick={this.exportReports}
              >
                导出报表
              </Button>
            </div>
          </div>
        </div>

        {/* 快速统计 */}
        {/* <Row gutter={[24, 24]} className="stats-section">
          <Col xs={24} sm={12} lg={6}>
            <Card>
              <Statistic
                title="总收入"
                value={overallStats.totalRevenue}
                prefix={<Icon type="dollar" />}
                precision={0}
                formatter={(value) => `¥${value.toLocaleString()}`}
                valueStyle={{ color: '#52c41a' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} lg={6}>
            <Card>
              <Statistic
                title="总支出"
                value={overallStats.totalCost}
                prefix={<Icon type="minus-circle" />}
                precision={0}
                formatter={(value) => `¥${value.toLocaleString()}`}
                valueStyle={{ color: '#ff4d4f' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} lg={6}>
            <Card>
              <Statistic
                title="净利润"
                value={overallStats.totalProfit}
                prefix={<Icon type="rise" />}
                precision={0}
                formatter={(value) => `¥${value.toLocaleString()}`}
                valueStyle={{ color: overallStats.totalProfit >= 0 ? '#1890ff' : '#ff4d4f' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} lg={6}>
            <Card>
              <Statistic
                title="利润率"
                value={overallStats.profitMargin}
                prefix={<Icon type="percentage" />}
                suffix="%"
                precision={1}
                valueStyle={{ color: '#722ed1' }}
              />
            </Card>
          </Col>
        </Row> */}

        <Card className="main-content-card">
          <Tabs
            activeKey={activeTab}
            onChange={this.handleTabChange}
            size="large"
          >
            {/* <TabPane
              tab={
                <span>
                  <Icon type="line-chart" />
                  财务报表
                </span>
              }
              key="financial"
            >
              <div className="report-content">
                <h3>财务报表</h3>
                <p>这里将显示详细的财务报表数据，包括收入、支出、利润等信息。</p>
                <div className="placeholder-chart">
                  <Icon type="bar-chart" style={{ fontSize: 64, color: '#d9d9d9' }} />
                  <p>图表组件开发中...</p>
                </div>
              </div>
            </TabPane>

            <TabPane
              tab={
                <span>
                  <Icon type="pie-chart" />
                  项目分析
                </span>
              }
              key="analysis"
            >
              <div className="report-content">
                <h3>项目分析</h3>
                <p>这里将显示项目的详细分析数据，包括项目进度、成本分析、ROI等。</p>
                <div className="placeholder-chart">
                  <Icon type="pie-chart" style={{ fontSize: 64, color: '#d9d9d9' }} />
                  <p>图表组件开发中...</p>
                </div>
              </div>
            </TabPane>

            <TabPane
              tab={
                <span>
                  <Icon type="fund" />
                  趋势分析
                </span>
              }
              key="trend"
            >
              <div className="report-content">
                <h3>趋势分析</h3>
                <p>这里将显示财务数据的趋势分析，包括月度、季度、年度趋势。</p>
                <div className="placeholder-chart">
                  <Icon type="fund" style={{ fontSize: 64, color: '#d9d9d9' }} />
                  <p>图表组件开发中...</p>
                </div>
              </div>
            </TabPane> */}

            <TabPane
              tab={
                <span>
                  <Icon type="tags" />
                  品牌汇总
                </span>
              }
              key="brand-summary"
            >
              <BrandSummary />
            </TabPane>

            <TabPane
              tab={
                <span>
                  <Icon type="deployment-unit" />
                  品牌执行
                </span>
              }
              key="brand-execution"
            >
              <BrandExecution />
            </TabPane>
          </Tabs>
        </Card>
      </div>
    );
  }
}

export default ReportsPage;
