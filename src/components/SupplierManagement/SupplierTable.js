import React, { useState, useEffect } from 'react';
import {
  Table,
  Button,
  Input,
  Select,
  Row,
  Col,
  Card,
  message,
  Modal,
  Tag,
  Popconfirm,
  Alert,
  // Space,
} from 'antd';
import { supplierAPI } from '../../services/api';
import {
  SUPPLIER_STATUS,
  SERVICE_TYPES,
  getSupplierStatusConfig,
  getServiceTypeLabel,
  formatDate,
  getRatingDisplay,
} from '../../utils/supplierUtils';
import { PageGuard, FeatureGuard, ButtonGuard } from '../Permission/PermissionGuard';
import { useFeaturePermission } from '../../hooks/usePermission';
import { PAGE_PERMISSIONS } from '../../config/permissions';

const { Search } = Input;
const { Option } = Select;
const { confirm } = Modal;

const SupplierTable = ({ onView, onEdit, onReady }) => {
  // 权限控制
  const {
    canRead,
    canDelete,
  } = useFeaturePermission('supplier');

  // 状态管理
  const [suppliers, setSuppliers] = useState([]);
  const [loading, setLoading] = useState(false);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 20,
    total: 0,
  });
  const [filters, setFilters] = useState({
    keyword: undefined,
    status: undefined,
    serviceType: undefined,
  });
  const [sorter, setSorter] = useState({
    field: 'createdAt',
    order: 'descend',
  });
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);

  // 加载供应商数据
  const loadSuppliers = async () => {
    setLoading(true);
    try {
      const params = {
        page: pagination.current,
        pageSize: pagination.pageSize,
        keyword: filters.keyword || undefined,
        status: filters.status || undefined,
        serviceType: filters.serviceType || undefined,
        sortBy: sorter.field,
        sortOrder: sorter.order === 'descend' ? 'desc' : 'asc',
      };

      const response = await supplierAPI.getSuppliers(params);
      if (response.success) {
        setSuppliers(response.data.suppliers);
        setPagination((prev) => ({
          ...prev,
          total: response.data.total,
        }));
      } else {
        message.error(response.message || '获取供应商列表失败');
      }
    } catch (error) {
      console.error('Load suppliers failed:', error);
      message.error('获取供应商列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 初始化数据
  useEffect(() => {
    if (canRead) {
      loadSuppliers();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [canRead]);

  useEffect(() => {
    if (onReady) {
      onReady({
        loadSuppliers: () => {
          loadSuppliers();
        },
      });
    }
  }, [onReady]);

  // 事件处理方法
  const handleTableChange = (paginationParam, _, sorterParam) => {
    setPagination((prev) => ({
      ...prev,
      current: paginationParam.current,
      pageSize: paginationParam.pageSize,
    }));
    setSorter({
      field: sorterParam.field || 'createdAt',
      order: sorterParam.order || 'descend',
    });

    // 延迟执行以确保状态更新
    setTimeout(() => {
      loadSuppliers();
    }, 0);
  };

  const handleSearch = (value) => {
    setFilters((prev) => ({
      ...prev,
      keyword: value,
    }));
    setPagination((prev) => ({
      ...prev,
      current: 1,
    }));

    setTimeout(() => {
      loadSuppliers();
    }, 0);
  };

  const handleFilterChange = (key, value) => {
    setFilters((prev) => ({
      ...prev,
      [key]: value,
    }));
    setPagination((prev) => ({
      ...prev,
      current: 1,
    }));

    setTimeout(() => {
      loadSuppliers();
    }, 0);
  };

  const handleDelete = async (id) => {
    try {
      const response = await supplierAPI.deleteSupplier(id);
      if (response.success) {
        message.success('删除成功');
        loadSuppliers();
      } else {
        message.error(response.message || '删除失败');
      }
    } catch (error) {
      console.error('Delete supplier failed:', error);
      message.error('删除失败');
    }
  };

  const handleBatchDelete = () => {
    if (selectedRowKeys.length === 0) {
      message.warning('请选择要删除的供应商');
      return;
    }

    confirm({
      title: '确认删除',
      content: `确定要删除选中的 ${selectedRowKeys.length} 个供应商吗？`,
      onOk: async () => {
        try {
          const response = await supplierAPI.batchDeleteSuppliers(selectedRowKeys);
          if (response.success) {
            message.success('批量删除成功');
            setSelectedRowKeys([]);
            loadSuppliers();
          } else {
            message.error(response.message || '批量删除失败');
          }
        } catch (error) {
          console.error('Batch delete failed:', error);
          message.error('批量删除失败');
        }
      },
    });
  };

  const handleRowSelectionChange = (keys) => {
    setSelectedRowKeys(keys);
  };

  // 权限检查 - 如果没有读取权限，显示无权限提示
  if (!canRead) {
    return (
      <div style={{ padding: '20px' }}>
        <Alert
          message="无权限访问"
          description="您没有权限查看供应商管理页面"
          type="warning"
          showIcon
        />
      </div>
    );
  }

  // 表格列定义（包含权限控制）
  const columns = [
    {
      title: '供应商名称',
      dataIndex: 'name',
      key: 'name',
      sorter: true,
      render: (text, record) => (
        <div>
          <div style={{ fontWeight: 'bold' }}>{text}</div>
          {record.shortName && (
            <div style={{ fontSize: '12px', color: '#666' }}>
              简称：{record.shortName}
            </div>
          )}
        </div>
      ),
      width: 200,
    },
    {
      title: '编码',
      dataIndex: 'code',
      key: 'code',
      width: 120,
    },
    {
      title: '联系人',
      dataIndex: 'contactPerson',
      key: 'contactPerson',
      width: 100,
    },
    {
      title: '联系方式',
      key: 'contact',
      width: 150,
      render: (_, record) => (
        <div>
          <div>{record.contactPhone}</div>
          {record.contactEmail && (
            <div style={{ fontSize: '12px', color: '#666' }}>
              {record.contactEmail}
            </div>
          )}
        </div>
      ),
    },
    {
      title: '服务类型',
      dataIndex: 'serviceTypes',
      key: 'serviceTypes',
      width: 150,
      render: (serviceTypes) => (
        <div>
          {serviceTypes?.map((type) => (
            <Tag key={type} size="small">
              {getServiceTypeLabel(type)}
            </Tag>
          ))}
        </div>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      sorter: true,
      render: (status) => {
        const config = getSupplierStatusConfig(status);
        return <Tag color={config.color}>{config.label}</Tag>;
      },
    },
    {
      title: '评级',
      dataIndex: 'rating',
      key: 'rating',
      width: 120,
      sorter: true,
      render: (rating) => getRatingDisplay(rating),
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 120,
      sorter: true,
      render: (date) => formatDate(date),
    },
    {
      title: '操作',
      key: 'action',
      width: 250,
      render: (_, record) => (
        <>
          <FeatureGuard permissions={['supplier.read']}>
            <Button
              type="link"
              size="small"
              onClick={() => onView && onView(record)}
            >
              查看
            </Button>
          </FeatureGuard>

          <ButtonGuard permissions={['supplier.update']}>
            <Button
              type="link"
              size="small"
              onClick={() => onEdit && onEdit(record)}
            >
              编辑
            </Button>
          </ButtonGuard>

          <ButtonGuard permissions={['supplier.delete']}>
            <Popconfirm
              title="确定要删除这个供应商吗？"
              onConfirm={() => handleDelete(record.id)}
              okText="确定"
              cancelText="取消"
            >
              <Button type="link" size="small" style={{ color: '#ff4d4f' }}>
                删除
              </Button>
            </Popconfirm>
          </ButtonGuard>
        </>
      ),
    },
  ];

  // 行选择配置
  const rowSelection = {
    selectedRowKeys,
    onChange: handleRowSelectionChange,
    getCheckboxProps: () => ({
      disabled: !canDelete, // 没有删除权限时禁用选择
    }),
  };

  return (
    <PageGuard permissions={PAGE_PERMISSIONS.SUPPLIER_MANAGEMENT}>
      <div style={{ padding: '20px' }}>

        <Card>
          <Row gutter={16} style={{ marginBottom: 16 }}>
            <Col span={8}>
              <Search
                placeholder="搜索供应商名称、联系人、联系方式"
                onSearch={handleSearch}
                style={{ width: '100%' }}
              />
            </Col>
            <Col span={4}>
              <Select
                placeholder="状态"
                style={{ width: '100%' }}
                value={filters.status}
                onChange={(value) => handleFilterChange('status', value)}
                allowClear
              >
                {SUPPLIER_STATUS.map((status) => (
                  <Option key={status.value} value={status.value}>
                    {status.label}
                  </Option>
                ))}
              </Select>
            </Col>
            <Col span={4}>
              <Select
                placeholder="服务类型"
                style={{ width: '100%' }}
                value={filters.serviceType}
                onChange={(value) => handleFilterChange('serviceType', value)}
                allowClear
              >
                {SERVICE_TYPES.map((type) => (
                  <Option key={type.value} value={type.value}>
                    {type.label}
                  </Option>
                ))}
              </Select>
            </Col>
            <Col span={8} style={{ textAlign: 'right' }}>
              <FeatureGuard permissions={['supplier.delete']}>
                <Button
                  type="danger"
                  onClick={handleBatchDelete}
                  disabled={selectedRowKeys.length === 0}
                  style={{ marginRight: 8 }}
                >
                  批量删除 ({selectedRowKeys.length})
                </Button>
              </FeatureGuard>
              <Button type="primary" onClick={() => loadSuppliers()}>
                刷新
              </Button>
            </Col>
          </Row>

          <Table
            columns={columns}
            dataSource={suppliers}
            rowKey="id"
            loading={loading}
            pagination={{
              ...pagination,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total, range) =>
                `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
            }}
            rowSelection={canDelete ? rowSelection : null}
            onChange={handleTableChange}
            scroll={{ x: 1200 }}
          />
        </Card>
      </div>
    </PageGuard>
  );
};

export default SupplierTable;
