import React, { Component } from 'react';
import {
  Form,
  Input,
  Select,
  Button,
  Card,
  Row,
  Col,
  InputNumber,
  message,
  Divider,
} from 'antd';
import { supplierAPI } from '../../services/api';
import {
  SERVICE_TYPES,
  TAX_RATES,
  SUPPLIER_RATINGS,
} from '../../utils/supplierUtils';

const { Option } = Select;
const { TextArea } = Input;

// 表单校验规则
const formRules = {
  name: [
    { required: true, message: '请输入供应商名称' },
    { min: 2, max: 100, message: '供应商名称长度应在2-100个字符之间' },
  ],
  code: [
    { required: true, message: '请输入供应商编码' },
    { min: 3, max: 20, message: '供应商编码长度应在3-20个字符之间' },
  ],
  contactPerson: [
    { required: true, message: '请输入联系人' },
    { min: 2, max: 50, message: '联系人长度应在2-50个字符之间' },
  ],
  contactPhone: [
    { required: true, message: '请输入联系电话' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码' },
  ],
  contactEmail: [
    { type: 'email', message: '请输入正确的邮箱地址' },
  ],
  serviceTypes: [
    { required: true, message: '请至少选择一种服务类型' },
    { type: 'array', min: 1, message: '请至少选择一种服务类型' },
  ],
  creditLimit: [
    { type: 'number', min: 0, message: '信用额度不能为负数' },
  ],
  rating: [
    { type: 'number', min: 1, max: 5, message: '评级必须在1-5之间' },
  ],
};

class SupplierForm extends Component {
  constructor(props) {
    super(props);
    this.state = {
      loading: false,
    };
  }

  componentDidMount() {
    // 如果是编辑模式，填充表单数据
    if (this.props.supplier) {
      this.props.form.setFieldsValue({
        name: this.props.supplier.name,
        shortName: this.props.supplier.shortName,
        code: this.props.supplier.code,
        contactPerson: this.props.supplier.contactPerson,
        contactPhone: this.props.supplier.contactPhone,
        contactEmail: this.props.supplier.contactEmail,
        address: this.props.supplier.address,
        taxNumber: this.props.supplier.taxNumber,
        bankAccount: this.props.supplier.bankAccount,
        bankName: this.props.supplier.bankName,
        legalPerson: this.props.supplier.legalPerson,
        serviceTypes: this.props.supplier.serviceTypes,
        preferredTaxRate: this.props.supplier.preferredTaxRate,
        creditLimit: this.props.supplier.creditLimit,
        paymentTerms: this.props.supplier.paymentTerms,
        rating: this.props.supplier.rating,
        notes: this.props.supplier.notes,
      });
    }
  }

  handleSubmit = (e) => {
    e.preventDefault();
    this.props.form.validateFields(async (err, values) => {
      if (err) {
        message.error('请检查表单填写是否正确');
        return;
      }

      this.setState({ loading: true });

      try {
        const supplierData = {
          ...values,
          status: 'active', // 默认状态为启用
        };

        let response;
        if (this.props.supplier) {
          // 编辑模式
          response = await supplierAPI.updateSupplier(this.props.supplier.id, supplierData);
        } else {
          // 新建模式
          response = await supplierAPI.createSupplier(supplierData);
        }

        if (response.success) {
          message.success(this.props.supplier ? '更新供应商成功' : '创建供应商成功');
          this.props.onSubmit && this.props.onSubmit(response.data);
        } else {
          message.error(response.message || '操作失败');
        }
      } catch (error) {
        console.error('Submit supplier failed:', error);
        message.error('操作失败');
      } finally {
        this.setState({ loading: false });
      }
    });
  };

  handleReset = () => {
    this.props.form.resetFields();
  };

  handleCancel = () => {
    this.props.onCancel && this.props.onCancel();
  };

  render() {
    const { loading } = this.state;
    const { supplier } = this.props;
    const { getFieldDecorator } = this.props.form;
    const isEdit = !!supplier;

    return (
      <Card title={isEdit ? '编辑供应商' : '新建供应商'} style={{ margin: '20px 0' }}>
        <Form layout="vertical" onSubmit={this.handleSubmit}>
          {/* 基本信息 */}
          <Card type="inner" title="基本信息" style={{ marginBottom: 16 }}>
            <Row gutter={16}>
              <Col span={8}>
                <Form.Item label="供应商名称">
                  {getFieldDecorator('name', {
                    rules: formRules.name,
                  })(
                    <Input placeholder="请输入供应商名称" />,
                  )}
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item label="简称">
                  {getFieldDecorator('shortName')(
                    <Input placeholder="请输入供应商简称" />,
                  )}
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item label="供应商编码">
                  {getFieldDecorator('code', {
                    rules: formRules.code,
                  })(
                    <Input placeholder="请输入供应商编码" />,
                  )}
                </Form.Item>
              </Col>
            </Row>
          </Card>

          {/* 联系信息 */}
          <Card type="inner" title="联系信息" style={{ marginBottom: 16 }}>
            <Row gutter={16}>
              <Col span={8}>
                <Form.Item label="联系人">
                  {getFieldDecorator('contactPerson', {
                    rules: formRules.contactPerson,
                  })(
                    <Input placeholder="请输入联系人" />,
                  )}
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item label="联系电话">
                  {getFieldDecorator('contactPhone', {
                    rules: formRules.contactPhone,
                  })(
                    <Input placeholder="请输入联系电话" />,
                  )}
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item label="联系邮箱">
                  {getFieldDecorator('contactEmail', {
                    rules: formRules.contactEmail,
                  })(
                    <Input placeholder="请输入联系邮箱" />,
                  )}
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={16}>
              <Col span={24}>
                <Form.Item label="地址">
                  {getFieldDecorator('address')(
                    <Input placeholder="请输入供应商地址" />,
                  )}
                </Form.Item>
              </Col>
            </Row>
          </Card>

          {/* 财务信息 */}
          <Card type="inner" title="财务信息" style={{ marginBottom: 16 }}>
            <Row gutter={16}>
              <Col span={8}>
                <Form.Item label="税号">
                  {getFieldDecorator('taxNumber')(
                    <Input placeholder="请输入税号" />,
                  )}
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item label="银行账号">
                  {getFieldDecorator('bankAccount')(
                    <Input placeholder="请输入银行账号" />,
                  )}
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item label="开户银行">
                  {getFieldDecorator('bankName')(
                    <Input placeholder="请输入开户银行" />,
                  )}
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={16}>
              <Col span={8}>
                <Form.Item label="法人代表">
                  {getFieldDecorator('legalPerson')(
                    <Input placeholder="请输入法人代表" />,
                  )}
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item label="首选税率">
                  {getFieldDecorator('preferredTaxRate')(
                    <Select placeholder="请选择首选税率">
                      {TAX_RATES.map((rate) => (
                        <Option key={rate.value} value={rate.value}>
                          {rate.label}
                        </Option>
                      ))}
                    </Select>,
                  )}
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item label="信用额度">
                  {getFieldDecorator('creditLimit', {
                    rules: formRules.creditLimit,
                  })(
                    <InputNumber
                      placeholder="请输入信用额度"
                      style={{ width: '100%' }}
                      min={0}
                      formatter={(value) => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                      parser={(value) => value.replace(/¥\s?|(,*)/g, '')}
                    />,
                  )}
                </Form.Item>
              </Col>
            </Row>
          </Card>

          {/* 业务信息 */}
          <Card type="inner" title="业务信息" style={{ marginBottom: 16 }}>
            <Row gutter={16}>
              <Col span={8}>
                <Form.Item label="服务类型">
                  {getFieldDecorator('serviceTypes', {
                    rules: formRules.serviceTypes,
                  })(
                    <Select
                      mode="multiple"
                      placeholder="请选择服务类型"
                    >
                      {SERVICE_TYPES.map((type) => (
                        <Option key={type.value} value={type.value}>
                          {type.label}
                        </Option>
                      ))}
                    </Select>,
                  )}
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item label="供应商评级">
                  {getFieldDecorator('rating', {
                    rules: formRules.rating,
                  })(
                    <Select placeholder="请选择供应商评级">
                      {SUPPLIER_RATINGS.map((rating) => (
                        <Option key={rating.value} value={rating.value}>
                          {rating.label}
                        </Option>
                      ))}
                    </Select>,
                  )}
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item label="付款条件">
                  {getFieldDecorator('paymentTerms')(
                    <Input placeholder="请输入付款条件" />,
                  )}
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={16}>
              <Col span={24}>
                <Form.Item label="备注信息">
                  {getFieldDecorator('notes')(
                    <TextArea
                      placeholder="请输入备注信息"
                      rows={4}
                    />,
                  )}
                </Form.Item>
              </Col>
            </Row>
          </Card>

          <Divider />

          <div style={{ textAlign: 'center' }}>
            <Button
              type="primary"
              htmlType="submit"
              loading={loading}
              style={{ marginRight: 16 }}
            >
              {isEdit ? '更新' : '创建'}
            </Button>
            <Button onClick={this.handleReset} style={{ marginRight: 16 }}>
              重置
            </Button>
            <Button onClick={this.handleCancel}>
              取消
            </Button>
          </div>
        </Form>
      </Card>
    );
  }
}

export default Form.create()(SupplierForm);
