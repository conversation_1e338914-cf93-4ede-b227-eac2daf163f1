import React, { useState, useRef } from 'react';
import { Card, Button, Modal, Alert } from 'antd';
import SupplierTable from './SupplierTable';
import SupplierFormModal from './SupplierFormModal';
import SupplierDetailModal from './SupplierDetailModal';
import SupplierStats from './SupplierStats';
import ErrorBoundary from '../Common/ErrorBoundary';
import { PageGuard, FeatureGuard, ButtonGuard } from '../Permission/PermissionGuard';
import { useFeaturePermission } from '../../hooks/usePermission';
import { PAGE_PERMISSIONS } from '../../config/permissions';

const SupplierManagement = () => {
  // 权限控制
  const {
    canRead,
    // canCreate,
    // canUpdate,
    // canDelete,
    // isSuperAdmin,
  } = useFeaturePermission('supplier');

  // 状态管理
  const [formModalVisible, setFormModalVisible] = useState(false);
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [statsModalVisible, setStatsModalVisible] = useState(false);
  const [selectedSupplier, setSelectedSupplier] = useState(null);

  // 表格引用,函数组件了,引用不到
  const tableRef = useRef(null);

  // 事件处理方法
  const handleCreateSupplier = () => {
    setSelectedSupplier(null);
    setFormModalVisible(true);
  };

  const handleViewSupplier = (supplier) => {
    setSelectedSupplier(supplier);
    setDetailModalVisible(true);
  };

  const handleEditSupplier = (supplier) => {
    setSelectedSupplier(supplier);
    setFormModalVisible(true);
  };

  const handleFormSubmit = () => {
    setFormModalVisible(false);
    setSelectedSupplier(null);
    // 刷新表格数据
    if (tableRef.current) {
      tableRef.current.loadSuppliers();
    }
  };

  const handleFormCancel = () => {
    setFormModalVisible(false);
    setSelectedSupplier(null);
  };

  const handleDetailModalCancel = () => {
    setDetailModalVisible(false);
    setSelectedSupplier(null);
  };

  const handleShowStats = () => {
    setStatsModalVisible(true);
  };

  const handleStatsModalCancel = () => {
    setStatsModalVisible(false);
  };
  const handleReady = (methods) => {
    tableRef.current = methods;
  };

  // 权限检查 - 如果没有读取权限，显示无权限提示
  if (!canRead) {
    return (
      <div style={{ padding: '20px' }}>
        <Alert
          message="无权限访问"
          description="您没有权限查看供应商管理页面"
          type="warning"
          showIcon
        />
      </div>
    );
  }

  return (
    <PageGuard permissions={PAGE_PERMISSIONS.SUPPLIER_MANAGEMENT}>
      <div style={{ padding: '20px' }}>
        {/* 权限状态提示 */}
        {/* <Alert
          message="权限控制已启用"
          description={
            <div>
              <p><strong>当前权限状态:</strong></p>
              <>
                <Tag color={canRead ? 'green' : 'red'}>查看: {canRead ? '✓' : '✗'}</Tag>
                <Tag color={canCreate ? 'green' : 'red'}>创建: {canCreate ? '✓' : '✗'}</Tag>
                <Tag color={canUpdate ? 'green' : 'red'}>编辑: {canUpdate ? '✓' : '✗'}</Tag>
                <Tag color={canDelete ? 'green' : 'red'}>删除: {canDelete ? '✓' : '✗'}</Tag>
                {isSuperAdmin && <Tag color="gold">超级管理员</Tag>}
              </>
            </div>
          }
          type="info"
          style={{ marginBottom: 16 }}
          closable
        /> */}

        {/* 主要内容区域 */}
        <Card
          title="供应商管理"
          extra={
            <>
              <ButtonGuard permissions={PAGE_PERMISSIONS.SUPPLIER_CREATE}>
                <Button
                  type="primary"
                  onClick={handleCreateSupplier}
                >
                  新建供应商
                </Button>
              </ButtonGuard>

              <FeatureGuard permissions={['supplier.read']}>
                <Button onClick={handleShowStats}>
                  供应商统计
                </Button>
              </FeatureGuard>
            </>
          }
        >
          <SupplierTable
            onReady={handleReady}
            onView={handleViewSupplier}
            onEdit={handleEditSupplier}
          />
        </Card>

        {/* 新建/编辑供应商弹窗 */}
        <FeatureGuard permissions={['supplier.create', 'supplier.update']}>
          <SupplierFormModal
            visible={formModalVisible}
            supplier={selectedSupplier}
            onOk={handleFormSubmit}
            onCancel={handleFormCancel}
          />
        </FeatureGuard>

        {/* 供应商详情弹窗 */}
        <FeatureGuard permissions={['supplier.read']}>
          <SupplierDetailModal
            visible={detailModalVisible}
            supplier={selectedSupplier}
            onCancel={handleDetailModalCancel}
          />
        </FeatureGuard>

        {/* 供应商统计弹窗 */}
        <FeatureGuard permissions={['supplier.read']}>
          <Modal
            title="供应商统计"
            visible={statsModalVisible}
            onCancel={handleStatsModalCancel}
            footer={null}
            width={1000}
            destroyOnClose
          >
            <ErrorBoundary
              title="统计数据加载失败"
              subTitle="无法显示统计数据，请稍后重试"
              onClose={handleStatsModalCancel}
            >
              <SupplierStats />
            </ErrorBoundary>
          </Modal>
        </FeatureGuard>
      </div>
    </PageGuard>
  );
};

export default SupplierManagement;
