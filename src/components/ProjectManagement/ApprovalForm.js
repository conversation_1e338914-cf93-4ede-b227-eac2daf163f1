import React, { Component } from 'react';
import {
  Modal,
  Form,
  Input,
  InputNumber,
  Select,
  DatePicker,
  Upload,
  Button,
  Row,
  Col,
  message,
  Icon,
  Card,
  Divider,
} from 'antd';
import moment from 'moment';
import { approvalAPI, supplierAPI, approvalUploadAPI } from '../../services/api';
import { formatCurrency } from '../../utils/weeklyBudgetUtils';
import { withStore } from '../../store/withStore';


// import { getCurrentUser } from '../../store/selectors';

const { Option } = Select;
const { TextArea } = Input;

// 合同签署主体选项
const CONTRACT_ENTITIES = [
  { value: 'company_a', label: '公司A' },
  { value: 'company_b', label: '公司B' },
  { value: 'subsidiary', label: '子公司' },
  { value: 'other', label: '其他' },
];

// 付款方式选项
const PAYMENT_METHODS = [
  { value: 'bank_transfer', label: '银行转账' },
  { value: 'online_payment', label: '在线支付' },
  { value: 'check', label: '支票' },
  { value: 'cash', label: '现金' },
  { value: 'other', label: '其他' },
];

class ApprovalForm extends Component {
  constructor(props) {
    super(props);
    this.state = {
      loading: false,
      invoiceFiles: [],
      attachments: [],
      uploadingInvoice: false,
      // uploadingAttachment: false,
    };
  }

  componentDidMount() {
    const { weeklyBudget } = this.props;
    if (weeklyBudget) {
      // 预填充表单数据
      this.props.form.setFieldsValue({
        totalAmount: weeklyBudget.contractAmount - (weeklyBudget.paidAmount || 0),
        paymentReason: `${weeklyBudget.title} - 服务费用`,
        expectedPaymentDate: moment().add(7, 'days'),
        paymentMethod: 'bank_transfer',
        contractEntity: 'company_a',
      });

      // 如果周预算有关联的供应商，获取供应商的收款账户信息
      if (weeklyBudget.supplierId) {
        this.loadSupplierAccountInfo(weeklyBudget.supplierId);
      }
    }
  }

  // 加载供应商收款账户信息
  loadSupplierAccountInfo = async (supplierId) => {
    try {
      const response = await supplierAPI.getSupplier(supplierId);
      if (response.success && response.data) {
        const supplier = response.data;

        // 自动填充收款账户信息
        if (supplier.bankAccount || supplier.bankName) {
          this.props.form.setFieldsValue({
            accountName: supplier.name, // 使用供应商名称作为账户名称
            accountNumber: supplier.bankAccount || '',
            bankName: supplier.bankName || '',
            bankCode: '', // 银行代码通常需要手动填写
          });

          message.info('已自动填入供应商收款账户信息');
        }
      }
    } catch (error) {
      console.error('Load supplier account info failed:', error);
      // 不显示错误信息，因为这不是关键功能
    }
  };

  handleSubmit = (e) => {
    e.preventDefault();
    this.props.form.validateFields(async (err, values) => {
      if (err) return;

      this.setState({ loading: true });
      try {
        const { weeklyBudget } = this.props;
        const { invoiceFiles, attachments } = this.state;

        const approvalData = {
          weeklyBudgetId: weeklyBudget.id,
          totalAmount: values.totalAmount,
          paymentReason: values.paymentReason,
          contractEntity: values.contractEntity,
          expectedPaymentDate: values.expectedPaymentDate.format('YYYY-MM-DD'),
          paymentMethod: values.paymentMethod,
          department: values.department,
          receivingAccount: {
            accountName: values.accountName,
            accountNumber: values.accountNumber,
            bankName: values.bankName,
            bankCode: values.bankCode,
          },
          supplierId: weeklyBudget.supplierId,
          relatedApprovalId: values.relatedApprovalId,
          invoiceFiles: invoiceFiles.map((file) => file.mediaId),
          attachments: attachments.map((file) => file.mediaId),
          remark: values.remark,
        };

        const response = await approvalAPI.createApproval(approvalData);
        if (response.success) {
          message.success('发起审批成功');
          this.props.onSubmit && this.props.onSubmit(response.data);
        } else {
          message.error(response.message || '发起审批失败');
        }
      } catch (error) {
        console.error('Create approval failed:', error);
        message.error('发起审批失败');
      } finally {
        this.setState({ loading: false });
      }
    });
  };

  handleCancel = () => {
    this.props.onCancel && this.props.onCancel();
  };

  // 处理发票文件上传
  handleInvoiceUpload = async (file) => {
    this.setState({ uploadingInvoice: true });
    try {
      const response = await approvalUploadAPI.uploadFile('invoice', file);
      if (response.success) {
        const newFile = {
          uid: file.uid,
          name: file.name,
          status: 'done',
          mediaId: response.data.mediaIds[0],
        };
        this.setState((prevState) => ({
          invoiceFiles: [...prevState.invoiceFiles, newFile],
        }));
        console.log('[ response.data.mediaIds ] >', this.state.invoiceFiles);
        message.success('发票上传成功');
      } else {
        message.error('发票上传失败');
      }
    } catch (error) {
      console.error('Upload invoice failed:', error);
      message.error('发票上传失败');
    } finally {
      this.setState({ uploadingInvoice: false });
    }
    return false; // 阻止默认上传行为
  };

  // 当前用户的部门,可能是多个
  getCurrentUserDepartments = () => {
    // store获取当前用户
    // const userDepartments = this.props.store.getState().departments;
    // console.log('[ userDepartments ] >', userDepartments);
    // return userDepartments;

    return [];
  };

  // 处理附件上传
  handleAttachmentUpload = async (file) => {
    // this.setState({ uploadingAttachment: true });
    try {
      const response = await approvalUploadAPI.uploadFile('attachment', file, true);
      if (response.success) {
        const newFile = {
          uid: file.uid,
          name: file.name,
          status: 'done',
          mediaId: response.data.mediaIds[0],
        };
        this.setState((prevState) => ({
          attachments: [...prevState.invoiceFiles, newFile],
        }));
        console.log('[ response.data.mediaIds ] >', this.state.attachments);
        message.success('附件上传成功');
      } else {
        message.error('附件上传失败');
      }
    } catch (error) {
      console.error('Upload attachment failed:', error);
      message.error('附件上传失败');
    } finally {
      // this.setState({ uploadingAttachment: false });
    }
    return false; // 阻止默认上传行为
  };

  // 删除发票文件
  handleRemoveInvoice = (file) => {
    this.setState((prevState) => ({
      invoiceFiles: prevState.invoiceFiles.filter((item) => item.uid !== file.uid),
    }));
  };

  // 删除附件
  handleRemoveAttachment = (file) => {
    this.setState((prevState) => ({
      attachments: prevState.attachments.filter((item) => item.uid !== file.uid),
    }));
  };

  handleSelectFromDingTalk = () => {
    // 获取spaceId
    dd.biz.cspace.preview({
      corpId: 'ding660cccf3aa1024874ac5d6980864d335',
      spaceId: '123456',
      onSuccess: (result) => {
        console.log('选择文件成功:', result);
      },
      onFail: (err) => {
        console.log('选择文件失败:', err);
      },
    });
  };

  render() {
    const { visible, weeklyBudget } = this.props;
    const {
      loading,
      invoiceFiles,
      // attachments,
      uploadingInvoice,
      // uploadingAttachment,
    } = this.state;
    const { getFieldDecorator } = this.props.form;
    const { currentUser, departmentMap } = this.props.store;
    const { deptIds } = currentUser || [];


    if (!weeklyBudget) return null;

    const remainingAmount = weeklyBudget.contractAmount - (weeklyBudget.paidAmount || 0);

    return (
      <Modal
        title="发起对公付款审批"
        visible={visible}
        onCancel={this.handleCancel}
        footer={null}
        width={800}
        destroyOnClose
      >
        <Card size="small" style={{ marginBottom: 16 }}>
          <h4>周预算信息</h4>
          <Row gutter={16}>
            <Col span={12}>
              <p><strong>预算标题：</strong>{weeklyBudget.title}</p>
              <p><strong>合同金额：</strong>{formatCurrency(weeklyBudget.contractAmount)}</p>
            </Col>
            <Col span={12}>
              <p><strong>已付金额：</strong>{formatCurrency(weeklyBudget.paidAmount || 0)}</p>
              <p><strong>剩余金额：</strong>{formatCurrency(remainingAmount)}</p>
            </Col>
          </Row>
        </Card>

        <Form onSubmit={this.handleSubmit} layout="vertical">
          <Row gutter={16}>
            <Col span={8}>
              <Form.Item label="请选择所在部门" required>
                {getFieldDecorator('department', {
                  rules: [{ required: true, message: '请选择所在部门' }],
                })(
                  <Select placeholder="请选择所在部门">
                    {deptIds.filter((item) => item !== 1).map((dept) => (
                      <Option key={dept} value={dept}>
                        {departmentMap[dept] && departmentMap[dept].name}
                      </Option>
                    ))}
                  </Select>,
                )}
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label="付款金额" required>
                {getFieldDecorator('totalAmount', {
                  rules: [
                    { required: true, message: '请输入付款金额' },
                    { type: 'number', min: 0.01, message: '付款金额必须大于0' },
                    {
                      validator: (rule, value, callback) => {
                        if (value && value > remainingAmount) {
                          callback('付款金额不能超过剩余金额');
                        }
                        callback();
                      },
                    },
                  ],
                })(
                  <InputNumber
                    style={{ width: '100%' }}
                    placeholder="请输入付款金额"
                    formatter={(value) => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                    parser={(value) => value.replace(/¥\s?|(,*)/g, '')}
                  />,
                )}
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label="期望付款时间" required>
                {getFieldDecorator('expectedPaymentDate', {
                  rules: [{ required: true, message: '请选择期望付款时间' }],
                })(
                  <DatePicker
                    style={{ width: '100%' }}
                    placeholder="请选择期望付款时间"
                    disabledDate={(current) => current && current < moment().startOf('day')}
                  />,
                )}
              </Form.Item>
            </Col>
          </Row>

          <Form.Item label="付款事由" required>
            {getFieldDecorator('paymentReason', {
              rules: [{ required: true, message: '请输入付款事由' }],
            })(
              <TextArea
                rows={3}
                placeholder="请详细描述付款事由"
                maxLength={500}
              />,
            )}
          </Form.Item>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item label="合同签署主体">
                {getFieldDecorator('contractEntity')(
                  <Select placeholder="请选择合同签署主体">
                    {CONTRACT_ENTITIES.map((entity) => (
                      <Option key={entity.value} value={entity.value}>
                        {entity.label}
                      </Option>
                    ))}
                  </Select>,
                )}
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="付款方式">
                {getFieldDecorator('paymentMethod')(
                  <Select placeholder="请选择付款方式">
                    {PAYMENT_METHODS.map((method) => (
                      <Option key={method.value} value={method.value}>
                        {method.label}
                      </Option>
                    ))}
                  </Select>,
                )}
              </Form.Item>
            </Col>
          </Row>

          <Divider orientation="left">收款账号信息</Divider>

          {weeklyBudget.supplier && (
            <div style={{ marginBottom: 16, padding: 8, backgroundColor: '#f6ffed', border: '1px solid #b7eb8f', borderRadius: 4 }}>
              <Icon type="info-circle" style={{ color: '#52c41a', marginRight: 8 }} />
              <span style={{ color: '#52c41a' }}>
                收款账户信息已从供应商【{weeklyBudget.supplier.name}】自动获取，您可以根据需要进行修改
              </span>
            </div>
          )}

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item label="账户名称" required>
                {getFieldDecorator('accountName', {
                  rules: [{ required: true, message: '请输入账户名称' }],
                })(
                  <Input placeholder="请输入账户名称" />,
                )}
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="账号" required>
                {getFieldDecorator('accountNumber', {
                  rules: [{ required: true, message: '请输入账号' }],
                })(
                  <Input placeholder="请输入账号" />,
                )}
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item label="开户银行" required>
                {getFieldDecorator('bankName', {
                  rules: [{ required: true, message: '请输入开户银行' }],
                })(
                  <Input placeholder="请输入开户银行" />,
                )}
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="银行代码">
                {getFieldDecorator('bankCode')(
                  <Input placeholder="请输入银行代码（可选）" />,
                )}
              </Form.Item>
            </Col>
          </Row>

          {/* 选择用户的部门,如果当前用户只有一个部门,就默认为该部门,如果有多个可以给改用户选择 */}
          {/* <Row gutter={16}>
            <Col span={12}>
              <Form.Item label="请选择所在部门" required>
                {getFieldDecorator('department', {
                  rules: [{ required: true, message: '请选择所在部门' }],
                })(
                  <Select placeholder="请选择所在部门">
                    {this.getCurrentUserDepartments().map((dept) => (
                      <Option key={dept.id} value={dept.id}>
                        {dept.name}
                      </Option>
                    ))}
                  </Select>,
                )}
              </Form.Item>
            </Col>
          </Row> */}

          <Form.Item label="关联审批单">
            {getFieldDecorator('relatedApprovalId')(
              <Input placeholder="请输入关联审批单号（可选）" />,
            )}
          </Form.Item>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item label="发票文件">
                <Upload
                  fileList={invoiceFiles}
                  beforeUpload={this.handleInvoiceUpload}
                  onRemove={this.handleRemoveInvoice}
                  multiple
                  accept=".pdf,.jpg,.jpeg,.png"
                >
                  <Button loading={uploadingInvoice}>
                    <Icon type="upload" /> 上传发票
                  </Button>
                </Upload>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="附件">
                {/* 从钉盘选择之类的 */}
                <Button type="dashed" onClick={this.handleSelectFromDingTalk}>从钉盘选择</Button>
                {/* <Upload
                  fileList={attachments}
                  beforeUpload={this.handleAttachmentUpload}
                  onRemove={this.handleRemoveAttachment}
                  multiple
                >
                  <Button loading={uploadingAttachment}>
                    <Icon type="upload" /> 上传附件
                  </Button>
                </Upload> */}
              </Form.Item>
            </Col>
          </Row>

          <Form.Item label="备注">
            {getFieldDecorator('remark')(
              <TextArea
                rows={3}
                placeholder="请输入备注信息（可选）"
                maxLength={500}
              />,
            )}
          </Form.Item>

          <Form.Item style={{ textAlign: 'right', marginTop: 24 }}>
            <Button onClick={this.handleCancel} style={{ marginRight: 8 }}>
              取消
            </Button>
            <Button type="primary" htmlType="submit" loading={loading}>
              发起审批
            </Button>
          </Form.Item>
        </Form>
      </Modal>
    );
  }
}

export default withStore(Form.create()(ApprovalForm));
